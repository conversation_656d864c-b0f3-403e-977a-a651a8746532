import React from 'react';
import ReactDOM from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
import { AuthProvider } from '@descope/react-sdk';
import { QueryProvider } from './providers/QueryProvider';
import { ErrorBoundary } from './components/ui/ErrorBoundary';
import { router } from './routes';
import './index.css';

const root = ReactDOM.createRoot(document.getElementById('root')!);

root.render(
    <React.StrictMode>
        <ErrorBoundary>
            <AuthProvider projectId={import.meta.env.VITE_DESCOPE_PROJECT_ID || "P2vKC8jjsUAuunFbeEFzZmkcbsbb"}>
                <QueryProvider>
                    <RouterProvider router={router} />
                </QueryProvider>
            </AuthProvider>
        </ErrorBoundary>
    </React.StrictMode>
);