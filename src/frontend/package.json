{"name": "my-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npx tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@descope/react-sdk": "^2.14.26", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}